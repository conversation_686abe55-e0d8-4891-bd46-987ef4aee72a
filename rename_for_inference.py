import os
import shutil
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# === CONFIGURATION (MODIFIEZ ICI) ===
# Chemin vers le dossier contenant les images à renommer
INPUT_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\imagesTestInference_original"

# Dossier de sortie (sera créé automatiquement)
OUTPUT_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\imagesTestInference_nnunet_format"

# Extensions d'images supportées
SUPPORTED_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.tiff', '.tif', '.bmp']

# === FONCTIONS ===
def validate_input_folder():
    """Vérifie que le dossier d'entrée existe et contient des images"""
    if not os.path.exists(INPUT_FOLDER):
        raise FileNotFoundError(f"Le dossier d'entrée n'existe pas: {INPUT_FOLDER}")
    
    # Compter les fichiers images
    image_files = []
    for ext in SUPPORTED_EXTENSIONS:
        image_files.extend([f for f in os.listdir(INPUT_FOLDER) if f.lower().endswith(ext.lower())])
    
    if not image_files:
        raise FileNotFoundError(f"Aucun fichier image trouvé dans {INPUT_FOLDER}")
    
    logger.info(f"[VALIDATION] Dossier d'entrée valide: {len(image_files)} images trouvées")
    return image_files

def create_output_folder():
    """Crée le dossier de sortie s'il n'existe pas"""
    if os.path.exists(OUTPUT_FOLDER):
        logger.warning(f"[WARNING] Le dossier de sortie existe déjà: {OUTPUT_FOLDER}")
        response = input("Voulez-vous continuer et écraser les fichiers existants? (y/N): ")
        if response.lower() != 'y':
            logger.info("Opération annulée par l'utilisateur")
            return False
        logger.info("Suppression du dossier existant...")
        shutil.rmtree(OUTPUT_FOLDER)
    
    os.makedirs(OUTPUT_FOLDER, exist_ok=True)
    logger.info(f"[SUCCESS] Dossier de sortie créé: {OUTPUT_FOLDER}")
    return True

def generate_nnunet_filename(index, original_extension='.png'):
    """Génère un nom de fichier au format nnU-Net: XXXX_0000.png"""
    # Format avec 4 chiffres pour l'index
    return f"{index:04d}_0000{original_extension}"

def rename_images():
    """Renomme toutes les images au format nnU-Net"""
    image_files = []
    
    # Collecter tous les fichiers images
    for filename in os.listdir(INPUT_FOLDER):
        file_path = os.path.join(INPUT_FOLDER, filename)
        if os.path.isfile(file_path):
            file_ext = Path(filename).suffix.lower()
            if file_ext in [ext.lower() for ext in SUPPORTED_EXTENSIONS]:
                image_files.append(filename)
    
    # Trier pour avoir un ordre cohérent
    image_files.sort()
    
    logger.info(f"[RENAME] Début du renommage de {len(image_files)} fichiers...")
    
    renamed_count = 0
    errors = []
    
    for index, original_filename in enumerate(image_files, start=1):
        try:
            # Chemin source
            source_path = os.path.join(INPUT_FOLDER, original_filename)
            
            # Nouveau nom (toujours en .png pour nnU-Net)
            new_filename = generate_nnunet_filename(index, '.png')
            destination_path = os.path.join(OUTPUT_FOLDER, new_filename)
            
            # Copier et convertir si nécessaire
            if original_filename.lower().endswith('.png'):
                # Simple copie pour les PNG
                shutil.copy2(source_path, destination_path)
            else:
                # Conversion vers PNG pour les autres formats
                try:
                    from PIL import Image
                    with Image.open(source_path) as img:
                        # Convertir en RGB si nécessaire (pour éviter les problèmes avec les PNG)
                        if img.mode in ('RGBA', 'LA', 'P'):
                            img = img.convert('RGB')
                        img.save(destination_path, 'PNG')
                except ImportError:
                    logger.error(f"PIL/Pillow non installé. Impossible de convertir {original_filename}")
                    # Fallback: copie simple avec extension .png
                    shutil.copy2(source_path, destination_path)
            
            logger.info(f"[RENAME] {original_filename} -> {new_filename}")
            renamed_count += 1
            
        except Exception as e:
            error_msg = f"Erreur lors du renommage de {original_filename}: {str(e)}"
            logger.error(f"[ERROR] {error_msg}")
            errors.append(error_msg)
    
    # Résumé
    logger.info(f"\n[SUMMARY] Renommage terminé:")
    logger.info(f"  - Fichiers traités avec succès: {renamed_count}")
    logger.info(f"  - Erreurs: {len(errors)}")
    
    if errors:
        logger.error(f"\n[ERRORS] Liste des erreurs:")
        for error in errors:
            logger.error(f"  - {error}")
    
    return renamed_count, errors

def create_mapping_file():
    """Crée un fichier de correspondance entre anciens et nouveaux noms"""
    mapping_file = os.path.join(OUTPUT_FOLDER, "filename_mapping.txt")
    
    image_files = []
    for filename in os.listdir(INPUT_FOLDER):
        file_path = os.path.join(INPUT_FOLDER, filename)
        if os.path.isfile(file_path):
            file_ext = Path(filename).suffix.lower()
            if file_ext in [ext.lower() for ext in SUPPORTED_EXTENSIONS]:
                image_files.append(filename)
    
    image_files.sort()
    
    with open(mapping_file, 'w', encoding='utf-8') as f:
        f.write("# Correspondance entre noms originaux et noms nnU-Net\n")
        f.write("# Format: nom_original -> nom_nnunet\n\n")
        
        for index, original_filename in enumerate(image_files, start=1):
            new_filename = generate_nnunet_filename(index, '.png')
            f.write(f"{original_filename} -> {new_filename}\n")
    
    logger.info(f"[MAPPING] Fichier de correspondance créé: {mapping_file}")

# === EXECUTION PRINCIPALE ===
def main():
    try:
        logger.info("[START] Début du renommage pour nnU-Net")
        logger.info(f"[CONFIG] Dossier d'entrée: {INPUT_FOLDER}")
        logger.info(f"[CONFIG] Dossier de sortie: {OUTPUT_FOLDER}")
        
        # 1. Validation du dossier d'entrée
        image_files = validate_input_folder()
        
        # 2. Création du dossier de sortie
        if not create_output_folder():
            return
        
        # 3. Renommage des images
        renamed_count, errors = rename_images()
        
        # 4. Création du fichier de correspondance
        create_mapping_file()
        
        # 5. Résumé final
        logger.info(f"\n[SUCCESS] Opération terminée!")
        logger.info(f"[RESULT] {renamed_count} fichiers renommés au format nnU-Net")
        logger.info(f"[RESULT] Dossier de sortie: {OUTPUT_FOLDER}")
        
        if errors:
            logger.warning(f"[WARNING] {len(errors)} erreurs détectées - vérifiez les logs ci-dessus")
        
        print(f"\n=== INSTRUCTIONS POUR L'INFÉRENCE ===")
        print(f"1. Utilisez ce dossier pour l'inférence: {OUTPUT_FOLDER}")
        print(f"2. Dans infer_nnunet_5fold.py, modifiez:")
        print(f"   INPUT_FOLDER = r\"{OUTPUT_FOLDER}\"")
        print(f"3. Le fichier filename_mapping.txt contient la correspondance des noms")
        
    except Exception as e:
        logger.error(f"[FATAL] Erreur fatale: {str(e)}")
        return False

if __name__ == "__main__":
    main()
