import os
import subprocess
from utils.visualisation import reconvert_predictions
from utils.overlay_manager import OverlayManager
from utils.label_analyzer import analyze_inference_results
import cv2
import logging
import re

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === CONFIGURATION ===
DATASET_ID = "011"  # Numéro du dataset seulement (comme dans train_nnunet_5fold)
CONFIGURATION = "2d"
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5
GPU_ID = "0"

# === VALIDATION CROISÉE ===
USE_CROSS_VALIDATION = True  # True = 5-fold CV, False = mode original "all"
VALIDATION_FOLDS = ["0", "1", "2", "3", "4"]  # Folds à utiliser pour l'inférence

# === DOSSIERS ===
# INPUT_FOLDER =r"C:\Users\<USER>\Documents\4Corrosion\Dataset\imagesTestInference_uint8_miniDataset"
# BASE_OUTPUT_ROOT = r"C:\Users\<USER>\Documents\4Corrosion\Results\inference"
INPUT_FOLDER = "/mnt/Datasets/imagesTestInference_uint8_miniDataset"
BASE_OUTPUT_ROOT = "/mnt/results/inference"
input_name = os.path.basename(INPUT_FOLDER.rstrip("\\/")).replace(" ", "_")
output_candidate = os.path.join(BASE_OUTPUT_ROOT, input_name)

OUTPUT_FOLDER = output_candidate
version = 2
while os.path.exists(OUTPUT_FOLDER):
    OUTPUT_FOLDER = f"{output_candidate}_v{version}"
    version += 1
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# === PATHS ===
# RAW_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw"
# PREPROCESSED_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_preprocessed"
# RESULTS_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Results\nnUNet_results"

# === CHEMINS (MODIFIEZ SELON VOTRE ENVIRONNEMENT) ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"

os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH
os.environ["CUDA_VISIBLE_DEVICES"] = GPU_ID

# === AFFICHAGE DE LA CONFIGURATION ===
logger.info("[START] Début de l'inférence nnUNet")
logger.info(f"[CONFIG] Configuration:")
logger.info(f"   - Dataset ID: {DATASET_ID}")
logger.info(f"   - Configuration: {CONFIGURATION}")
logger.info(f"   - Époques: {EPOCHS}")
logger.info(f"   - Validation croisée: {USE_CROSS_VALIDATION}")
if USE_CROSS_VALIDATION:
    logger.info(f"   - Folds utilisés: {VALIDATION_FOLDS}")
logger.info(f"   - GPU: {GPU_ID}")
logger.info(f"   - Trainer: nnUNetTrainer_{EPOCHS}epochs")
logger.info(f"   - Dossier d'entrée: {INPUT_FOLDER}")
logger.info(f"   - Dossier de sortie: {OUTPUT_FOLDER}")

# === VALIDATION DES NOMS DE FICHIERS ===
def find_dataset_folder() -> str:
    """Trouve automatiquement le dossier du dataset dans les résultats nnUNet"""
    dataset_prefix = f"Dataset{DATASET_ID}_"
    try:
        # Chercher dans le dossier des résultats
        if not os.path.exists(RESULTS_PATH):
            raise FileNotFoundError(f"[ERROR] Dossier des résultats non trouvé: {RESULTS_PATH}")

        dataset_name = next(
            (d for d in os.listdir(RESULTS_PATH) if d.startswith(dataset_prefix)),
            None
        )
        if dataset_name is None:
            # Essayer avec juste le numéro si aucun suffixe trouvé
            exact_name = f"Dataset{DATASET_ID}"
            if exact_name in os.listdir(RESULTS_PATH):
                dataset_name = exact_name
            else:
                available_datasets = [d for d in os.listdir(RESULTS_PATH) if d.startswith("Dataset")]
                raise FileNotFoundError(
                    f"[ERROR] Aucun dataset commençant par '{dataset_prefix}' ou '{exact_name}' trouvé dans {RESULTS_PATH}\n"
                    f"Datasets disponibles: {available_datasets}"
                )

        logger.info(f"[DATASET] Dataset trouvé: {dataset_name}")
        return dataset_name
    except Exception as e:
        logger.error(f"Erreur lors de la recherche du dataset: {e}")
        raise

def validate_nnunet_naming(images_dir):
    """
    Vérifie que les fichiers respectent le format nnU-Net : {id}_0000.png
    Lève une exception si des fichiers ne sont pas conformes.
    """
    print(f"\n[INFO] Validation des noms de fichiers dans {images_dir}...")

    png_files = [f for f in os.listdir(images_dir) if f.lower().endswith('.png')]
    if not png_files:
        raise FileNotFoundError(f"Aucun fichier PNG trouvé dans {images_dir}")

    invalid_files = []
    valid_pattern = re.compile(r'^\d{4}_0000\.png$')

    for fname in png_files:
        if not valid_pattern.match(fname):
            invalid_files.append(fname)

    if invalid_files:
        logger.error(f"[ERROR] Fichiers avec noms non conformes au format nnU-Net (XXXX_0000.png):")
        for fname in invalid_files:
            logger.error(f"  - {fname}")
        raise ValueError(f"Fichiers non conformes détectés. Format attendu: XXXX_0000.png (ex: 0001_0000.png)")

    logger.info(f"[SUCCESS] Tous les {len(png_files)} fichiers respectent le format nnU-Net")
    return True

# === RECHERCHE DU DATASET ===
print("\n[INFO] Recherche du dataset...")
dataset_name = find_dataset_folder()

# === VALIDATION DES IMAGES D'ENTRÉE ===
print("\n[INFO] Validation des noms de fichiers...")
validate_nnunet_naming(INPUT_FOLDER)

# === VÉRIFICATION DES MODÈLES DISPONIBLES ===
trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
trainer_path = os.path.join(RESULTS_PATH, dataset_name, f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}")

def check_model_availability():
    """Vérifie que les modèles requis sont disponibles avant l'inférence"""
    if not os.path.exists(trainer_path):
        raise FileNotFoundError(
            f"[ERROR] Dossier du trainer non trouvé: {trainer_path}\n"
            f"Vérifiez que l'entraînement a été effectué avec les bons paramètres:\n"
            f"  - DATASET_ID: {DATASET_ID}\n"
            f"  - EPOCHS: {EPOCHS}\n"
            f"  - CONFIGURATION: {CONFIGURATION}"
        )

    if USE_CROSS_VALIDATION:
        # Vérifier les folds individuels
        missing_folds = []
        for fold in VALIDATION_FOLDS:
            fold_path = os.path.join(trainer_path, f"fold_{fold}")
            checkpoint_path = os.path.join(fold_path, "checkpoint_final.pth")
            if not os.path.exists(checkpoint_path):
                missing_folds.append(fold)

        if missing_folds:
            available_folds = []
            for item in os.listdir(trainer_path):
                if item.startswith("fold_") and os.path.isdir(os.path.join(trainer_path, item)):
                    checkpoint = os.path.join(trainer_path, item, "checkpoint_final.pth")
                    if os.path.exists(checkpoint):
                        available_folds.append(item.replace("fold_", ""))

            raise FileNotFoundError(
                f"[ERROR] Modèles manquants pour la validation croisée:\n"
                f"  - Folds demandés: {VALIDATION_FOLDS}\n"
                f"  - Folds manquants: {missing_folds}\n"
                f"  - Folds disponibles: {available_folds}\n"
                f"Solution: Modifiez VALIDATION_FOLDS pour utiliser seulement les folds disponibles\n"
                f"ou changez USE_CROSS_VALIDATION = False si fold_all existe."
            )
    else:
        # Vérifier fold_all
        fold_all_path = os.path.join(trainer_path, "fold_all")
        checkpoint_path = os.path.join(fold_all_path, "checkpoint_final.pth")

        if not os.path.exists(checkpoint_path):
            # Chercher les folds disponibles comme alternative
            available_folds = []
            for item in os.listdir(trainer_path):
                if item.startswith("fold_") and item != "fold_all" and os.path.isdir(os.path.join(trainer_path, item)):
                    checkpoint = os.path.join(trainer_path, item, "checkpoint_final.pth")
                    if os.path.exists(checkpoint):
                        available_folds.append(item.replace("fold_", ""))

            error_msg = (
                f"[ERROR] Modèle 'fold_all' non trouvé: {checkpoint_path}\n"
                f"Le dataset {dataset_name} n'a pas été entraîné en mode 'all'.\n"
            )

            if available_folds:
                error_msg += (
                    f"Solutions disponibles:\n"
                    f"  1. Utilisez la validation croisée:\n"
                    f"     USE_CROSS_VALIDATION = True\n"
                    f"     VALIDATION_FOLDS = {available_folds}\n"
                    f"  2. Entraînez un modèle en mode 'all':\n"
                    f"     Dans train_nnunet_5fold.py: USE_CROSS_VALIDATION = False"
                )
            else:
                error_msg += (
                    f"Aucun modèle trouvé. Vérifiez que l'entraînement s'est terminé correctement."
                )

            raise FileNotFoundError(error_msg)

# Vérifier la disponibilité des modèles
logger.info(f"[CHECK] Vérification des modèles disponibles...")
check_model_availability()
logger.info(f"[CHECK] ✅ Modèles disponibles et compatibles")

# === CONSTRUCTION DE LA COMMANDE D'INFÉRENCE ===
# Déterminer les folds à utiliser
if USE_CROSS_VALIDATION:
    folds_str = " ".join(VALIDATION_FOLDS)
    logger.info(f"[INFERENCE] Mode validation croisée avec folds: {VALIDATION_FOLDS}")
else:
    folds_str = "all"
    logger.info(f"[INFERENCE] Mode 'all' - utilisation du modèle entraîné sur toutes les données")

cmd_predict = (
    f'nnUNetv2_predict -d {dataset_name} -i "{INPUT_FOLDER}" -o "{OUTPUT_FOLDER}" '
    f'-f {folds_str} -tr {trainer_class} -c {CONFIGURATION} -p {PLANS_NAME}'
)

def run(cmd):
    print(f"\n[INFO] Lancement : {cmd}\n")
    subprocess.run(cmd, shell=True, check=True)

print(f"[INFO] Dossier de sortie : {OUTPUT_FOLDER}")
run(cmd_predict)

# === POST-INFERENCE : Reconversion + Visualisation ===
RECONVERTED_MASKS = os.path.join(OUTPUT_FOLDER, "reconverted_masks")
OVERLAY_DIR = os.path.join(OUTPUT_FOLDER, "overlays")

print("\n[INFO] Post-traitement des masques prédits...")
reconvert_predictions(
    input_dir=OUTPUT_FOLDER,
    output_dir=RECONVERTED_MASKS,
    class_to_value={
        0: 0,
        1: 29,
        2: 149,
        3: 76,
        4: 125
    }
)

print("\n[INFO] Création des overlays...")
os.makedirs(OVERLAY_DIR, exist_ok=True)
overlay_manager = OverlayManager()

for img_name in os.listdir(INPUT_FOLDER):
    if img_name.endswith(('.png', '.jpg', '.jpeg')):
        try:
            img_path = os.path.join(INPUT_FOLDER, img_name)
            logger.info(f"Traitement de l'image: {img_name}")
            original_img = cv2.imread(img_path)
            if original_img is None:
                logger.error(f"Impossible de charger l'image: {img_path}")
                continue

            img_number = img_name.split('_')[0]
            mask_name = f"{img_number}.png"
            mask_path = os.path.join(RECONVERTED_MASKS, mask_name)

            if not os.path.exists(mask_path):
                logger.error(f"Masque non trouvé: {mask_path}")
                continue

            mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
            if mask_img is None:
                logger.error(f"Impossible de charger le masque: {mask_path}")
                continue

            overlay = overlay_manager.create_high_contrast_overlay(original_img, mask_img, alpha=0.8)
            output_path = os.path.join(OVERLAY_DIR, f"overlay_{img_name}")
            overlay_manager.save_overlay(overlay, output_path)
            logger.info(f"Overlay sauvegardé: {output_path}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement de {img_name}: {str(e)}")
            continue

# === POSTPROCESSING ===
print("\n[INFO] Application du postprocessing...")

# Chemin vers le dossier des résultats du training
if USE_CROSS_VALIDATION:
    # Mode validation croisée
    RESULT_TRAINING_DIR = os.path.join(
        RESULTS_PATH, dataset_name, f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}",
        f"crossval_results_folds_{'_'.join(VALIDATION_FOLDS)}"
    )
    logger.info(f"[POSTPROCESSING] Mode validation croisée - dossier: {RESULT_TRAINING_DIR}")
else:
    # Mode "all" - utilise fold_all si disponible, sinon fold_0
    fold_all_dir = os.path.join(RESULTS_PATH, dataset_name, f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}", "fold_all")
    fold_0_dir = os.path.join(RESULTS_PATH, dataset_name, f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}", "fold_0")

    if os.path.exists(fold_all_dir):
        RESULT_TRAINING_DIR = fold_all_dir
        logger.info(f"[POSTPROCESSING] Mode 'all' - dossier: {RESULT_TRAINING_DIR}")
    elif os.path.exists(fold_0_dir):
        RESULT_TRAINING_DIR = fold_0_dir
        logger.info(f"[POSTPROCESSING] Mode 'all' - utilise fold_0: {RESULT_TRAINING_DIR}")
    else:
        RESULT_TRAINING_DIR = fold_all_dir  # Utiliser fold_all par défaut pour l'erreur
        logger.warning(f"[POSTPROCESSING] Aucun fold trouvé pour le mode 'all'")

POSTPROCESSING_PKL = os.path.join(RESULT_TRAINING_DIR, "postprocessing.pkl")
PLANS_JSON = os.path.join(RESULT_TRAINING_DIR, "plans.json")
OUTPUT_FOLDER_PP = OUTPUT_FOLDER + "_PP"

# Vérifier que les fichiers de postprocessing existent
if os.path.exists(POSTPROCESSING_PKL) and os.path.exists(PLANS_JSON):
    cmd_postprocess = (
        f'nnUNetv2_apply_postprocessing -i "{OUTPUT_FOLDER}" -o "{OUTPUT_FOLDER_PP}" '
        f'-pp_pkl_file "{POSTPROCESSING_PKL}" -np 1 -plans_json "{PLANS_JSON}"'
    )
    run(cmd_postprocess)
else:
    logger.warning(f"[WARNING] Fichiers de postprocessing non trouvés:")
    logger.warning(f"  - {POSTPROCESSING_PKL}")
    logger.warning(f"  - {PLANS_JSON}")
    logger.warning(f"[WARNING] Postprocessing ignoré")

# === ANALYSE DES LABELS ===
print("\n[INFO] Analyse des labels dans les masques...")
try:
    total_files, successful_analyses, failed_analyses, report_path = analyze_inference_results(OUTPUT_FOLDER, verbose=True)
    logger.info(f"[LABEL_ANALYSIS] Analyse terminée: {successful_analyses}/{total_files} fichiers analysés avec succès")
    if report_path:
        logger.info(f"[LABEL_ANALYSIS] Rapport sauvegardé: {report_path}")
except Exception as e:
    logger.error(f"[ERROR] Erreur lors de l'analyse des labels: {e}")

print("\n[SUCCESS] Inférence, post-traitement, postprocessing et analyse des labels terminés.")
